{"name": "apollo-docs", "version": "0.1.0", "private": true, "scripts": {"start": "next start", "lint": "biome check", "format": "biome format --write", "dev": "storybook dev -p 6006", "build-storybook": "storybook build", "publish:chromatic": "pnpm chromatic --project-token=chpt_d303073465f5af0 --exit-zero-on-changes"}, "dependencies": {"@storybook/addon-links": "^9.1.5", "next": "15.5.0", "react": "19.1.0", "react-dom": "19.1.0", "react-syntax-highlighter": "^15.6.1", "class-variance-authority": "0.7.1", "classnames": "2.5.1", "date-fns": "2.30.0", "lucide-react": "0.288.0", "zod": "^3.24.2"}, "devDependencies": {"@apollo/storefront": "workspace:*", "@apollo/ui": "workspace:*", "@biomejs/biome": "2.2.0", "@chromatic-com/storybook": "^4.1.1", "@design-systems/apollo-icons": "workspace:*", "@storybook/addon-a11y": "^9.1.4", "@storybook/addon-designs": "^10.0.2", "@storybook/addon-docs": "^9.1.4", "@storybook/addon-vitest": "^9.1.4", "@storybook/nextjs-vite": "^9.1.4", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "chromatic": "^13.1.4", "eslint-plugin-storybook": "^9.1.4", "playwright": "^1.55.0", "storybook": "^9.1.4", "storybook-addon-tag-badges": "^2.0.2", "tailwindcss": "^4", "typescript": "^5", "vitest": "^3.2.4"}}