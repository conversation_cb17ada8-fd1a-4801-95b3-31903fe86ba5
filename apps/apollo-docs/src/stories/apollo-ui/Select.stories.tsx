import React, { useMemo, useState } from "react"
import { ComponentRules, UsageGuidelines } from "@/components"
import { <PERSON>ton, Select, Typography } from "@apollo/ui"
import { InfoCircle } from "@design-systems/apollo-icons"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { <PERSON>a, StoryObj } from "@storybook/react"

/**
 * Select component
 *
 * The Select component lets users choose a single value from a list of options.
 * It supports label, helper text, required indicator, disabled and error states,
 * full-width layout, small/medium sizes, placeholders, and controlled/uncontrolled usage.
 *
 * Notes:
 * - Default size is "medium"
 * - Use <Select.Option label="..." value={...} /> for options
 */
const meta = {
  title: "@apollo∕ui/Components/Inputs/Select",
  component: Select,
  subcomponents: { Option: Select.Option },
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2294-1987&m=dev",
    },
    docs: {
      description: {
        component:
          "The Select component renders a dropdown for choosing a single value. It composes Field/Input internally to provide consistent label, helper text, and validation styling.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source code={`import { Select } from "@apollo/ui"`} language="tsx" />

          <h2 id="select-props">Props</h2>
          <ArgTypes />

          <h2 id="select-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Provide clear, concise labels to help users understand their choices.",
              "Ensure the default selection is meaningful to prevent unnecessary user actions.",
              "Use “Select” as a placeholder option only if there’s no logical default option.",
            ]}
          />

          <h2 id="select-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>Always provide a <code>label</code> and use <code>required</code> where needed.</>,
              <>Pair <code>error</code> with <code>helperText</code> to describe the issue.</>,
              <>Use <code>placeholder</code> to guide users, but avoid relying on it as the sole label.</>,
              <>Keep option labels concise and descriptive.</>,
              <>Keyboard users can open with Enter/Space and navigate with arrow keys.</>,
            ]}
          />

          <h2 id="select-examples">Examples</h2>
          <Stories title="" />

          <h2 id="select-dos-donts">Do’s and Don’ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ minWidth: 260 }}>
                      <Select label="Category" placeholder="Choose one">
                        <Select.Option label="Electronics" value="electronics" />
                        <Select.Option label="Groceries" value="groceries" />
                      </Select>
                    </div>
                  ),
                  description:
                    "Use a clear label and concise placeholder to guide users.",
                },
                negative: {
                  component: (
                    <div style={{ minWidth: 260 }}>
                      <Select
                        label="Choose from a very long and descriptive message that belongs in helper text"
                        placeholder="Pick one"
                      >
                        <Select.Option label="Electronics" value="electronics" />
                        <Select.Option label="Groceries" value="groceries" />
                      </Select>
                    </div>
                  ),
                  description:
                    "Avoid placing instructions in the label; use helperText instead.",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ minWidth: 260 }}>
                      <Select label="Category" placeholder="Choose one">
                        <Select.Option label="None" value="-" />
                        <Select.Option label="Electronics" value="electronics" />
                        <Select.Option label="Groceries" value="groceries" />
                      </Select>
                    </div>
                  ),
                  description:
                    "In case the user wants to reset the selection to an empty value, an additional choice representing an empty option must be added to the list.",
                },
                negative: {
                  component: (
                    <div style={{ minWidth: 260 }}>
                      <Select
                        label="Choose country"
                        placeholder="Pick one"
                      >
                        <Select.Option label="United States" value="us" />
                        <Select.Option label="Canada" value="ca" />
                        <Select.Option label="Mexico" value="mx" />
                        <Select.Option label="United Kingdom" value="uk" />
                        <Select.Option label="France" value="fr" />
                        <Select.Option label="Germany" value="de" />
                        <Select.Option label="Italy" value="it" />
                        <Select.Option label="Spain" value="es" />
                        <Select.Option label="Portugal" value="pt" />
                        <Select.Option label="Netherlands" value="nl" />
                        <Select.Option label="Belgium" value="be" />
                        <Select.Option label="Sweden" value="se" />
                        <Select.Option label="Norway" value="no" />
                        <Select.Option label="Denmark" value="dk" />
                        <Select.Option label="Finland" value="fi" />
                        <Select.Option label="Iceland" value="is" />
                        
                      </Select>
                    </div>
                  ),
                  description:
                    "Avoid using Select if the list is too long and needs search functionality",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    size: {
      control: { type: "radio" },
      options: ["small", "medium"],
      description: "Size of the input.",
      table: { type: { summary: '"small" | "medium"' }, defaultValue: { summary: "medium" } },
    },
    label: { control: "text", description: "Field label." },
    labelDecorator: {
      control: false,
      description: "Element displayed next to the label (ReactNode).",
      table: { type: { summary: "ReactNode" } },
    },
    helperText: { control: "text", description: "Helper or error text below the field." },
    helperTextDecorator: {
      control: false,
      description: "Element displayed next to helper text (ReactNode).",
      table: { type: { summary: "ReactNode" } },
    },
    fullWidth: { control: "boolean", description: "Stretch to the container width." },
    required: { control: "boolean", description: "Mark the field as required." },
    error: { control: "boolean", description: "Error state (affects styles)." },
    disabled: { control: "boolean", description: "Disable the field." },
    placeholder: { control: "text", description: "Placeholder text when no value is selected." },

    value: {
      control: false,
      description: "Controlled value.",
      table: { type: { summary: "any" } },
    },
    defaultValue: {
      control: false,
      description: "Uncontrolled default value.",
      table: { type: { summary: "any" } },
    },
    onChange: {
      control: false,
      description: "Callback fired when value changes: (value: any) => void",
      table: { type: { summary: "(value: any) => void" } },
    },
    fieldProps: { control: false, description: "Props for internal Field component." },
    ref: { control: false, table: { type: { summary: "Ref<HTMLElement>" } } },
    children: { control: false, description: "Select.Option elements." },
    className: { control: false },
  },
  args: {
    placeholder: "Select an option",
    fullWidth: false,
    disabled: false,
    error: false,
    required: false,
    size: "medium",
  },
} satisfies Meta<typeof Select>

export default meta

type Story = StoryObj<typeof Select>

const baseOptions = [
  { label: "Electronics — Laptops", value: "laptops" },
  { label: "Home & Kitchen — Cookware", value: "cookware" },
  { label: "Clothing — Men's Jackets", value: "mens-jackets" },
  { label: "Beauty & Personal Care", value: "beauty" },
]

/** Default Select (basic usage) */
export const Overview: Story = {
  args: { label: "Label", helperText: "Helper text" },
  render: (args) => (
    <div style={{ minWidth: 260 }}>
      <Select {...args}>
        {baseOptions.map((o) => (
          <Select.Option key={o.value} label={o.label} value={o.value} />
        ))}
      </Select>
    </div>
  ),
}

/** Sizes: small and medium */
export const Sizes: Story = {
  render: (args) => (
    <div style={{ display: "flex", gap: 16 }}>
      <div style={{ minWidth: 220 }}>
        <Select {...args} size="small" label="Small" placeholder="Small size">
          {baseOptions.map((o) => (
            <Select.Option key={o.value} label={o.label} value={o.value} />
          ))}
        </Select>
      </div>
      <div style={{ minWidth: 220 }}>
        <Select {...args} size="medium" label="Medium" placeholder="Medium size">
          {baseOptions.map((o) => (
            <Select.Option key={o.value} label={o.label} value={o.value} />
          ))}
        </Select>
      </div>
    </div>
  ),
}

/** Full width layout */
export const FullWidth: Story = {
  parameters: { layout: "padded" },
  args: { label: "Label", helperText: "Helper text", fullWidth: true },
  render: (args) => (
    <Select {...args}>
      {baseOptions.map((o) => (
        <Select.Option key={o.value} label={o.label} value={o.value} />
      ))}
    </Select>
  ),
}

/** Common states: default, disabled, error, decorators */
export const States: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Side-by-side comparison of common states: default, with decorators, disabled, error.",
      },
    },
  },
  render: (args) => (
    <div
      style={{
        display: "grid",
        gridTemplateColumns: "repeat(2, minmax(260px, 1fr))",
        gap: 20,
        alignItems: "end",
      }}
    >
      <Select {...args} label="Label" helperText="Helper text">
        {baseOptions.map((o) => (
          <Select.Option key={o.value} label={o.label} value={o.value} />
        ))}
      </Select>
      <Select
        {...args}
        label="Label"
        labelDecorator={<InfoCircle size={12} />}
        helperText="Helper text"
        helperTextDecorator={<InfoCircle size={12} />}
        placeholder="With decorators"
      >
        {baseOptions.map((o) => (
          <Select.Option key={o.value} label={o.label} value={o.value} />
        ))}
      </Select>
      <Select {...args} disabled placeholder="Disabled">
        {baseOptions.map((o) => (
          <Select.Option key={o.value} label={o.label} value={o.value} />
        ))}
      </Select>
      <Select
        {...args}
        disabled
        defaultValue={baseOptions[0].value}
        placeholder="Disabled with value"
      >
        {baseOptions.map((o) => (
          <Select.Option key={o.value} label={o.label} value={o.value} />
        ))}
      </Select>
      <Select
        {...args}
        error
        label="Label"
        helperText="This field is required"
        placeholder="Error state"
      >
        {baseOptions.map((o) => (
          <Select.Option key={o.value} label={o.label} value={o.value} />
        ))}
      </Select>
      <Select
        {...args}
        error
        label="Label"
        helperText="This field is required"
        helperTextDecorator={<InfoCircle size={12} />}
        placeholder="Error with decorator"
      >
        {baseOptions.map((o) => (
          <Select.Option key={o.value} label={o.label} value={o.value} />
        ))}
      </Select>
    </div>
  ),
}

/** Required indicator */
export const Required: Story = {
  args: { label: "Email", placeholder: "Select", required: true },
  render: (args) => (
    <Select {...args}>
      {baseOptions.map((o) => (
        <Select.Option key={o.value} label={o.label} value={o.value} />
      ))}
    </Select>
  ),
}

/** Controlled example */
export const Controlled: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Controlled example: use state to manage the current value and update via onChange.",
      },
    },
  },
  render: (args) => {
    const [value, setValue] = useState<string | undefined>()
    return (
      <div style={{ display: "flex", flexDirection: "column", gap: 8, minWidth: 300 }}>
        <Typography level="bodyLarge">{`Selected: ${value ?? "(none)"}`}</Typography>
        <Select {...args} value={value} onChange={(v) => setValue(v as string)}>
          {baseOptions.map((o) => (
            <Select.Option key={o.value} label={o.label} value={o.value} />
          ))}
        </Select>
        <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
          <Button size="small" onClick={() => setValue(baseOptions[0].value)}>Set first</Button>
          <Button size="small" onClick={() => setValue(baseOptions[2].value)}>Set third</Button>
          <Button size="small" onClick={() => setValue(undefined)}>Clear</Button>
        </div>
      </div>
    )
  },
  args: { label: "Label", fullWidth: true },
}

/** Dynamic value types (numbers) */
export const NumberValues: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates using numeric values with Select.Option. Both controlled and defaultValue patterns are supported.",
      },
    },
  },
  render: (args) => {
    const options = useMemo(
      () => [
        { label: "Ten", value: 10 },
        { label: "Twenty", value: 20 },
        { label: "Thirty", value: 30 },
      ],
      []
    )
    const [n, setN] = useState<number | undefined>()
    return (
      <div style={{ display: "grid", gridTemplateColumns: "repeat(2, 1fr)", gap: 16, minWidth: 420 }}>
        <div>
          <Typography level="bodyLarge">Controlled</Typography>
          <Select {...args} value={n} onChange={(v) => setN(v as number)}>
            {options.map((o) => (
              <Select.Option key={o.value} label={o.label} value={o.value} />
            ))}
          </Select>
        </div>
        <div>
          <Typography level="bodyLarge">Uncontrolled (defaultValue)</Typography>
          <Select {...args} defaultValue={20}>
            {options.map((o) => (
              <Select.Option key={o.value} label={o.label} value={o.value} />
            ))}
          </Select>
        </div>
      </div>
    )
  },
  args: { label: "Label", placeholder: "Pick a number" },
}

